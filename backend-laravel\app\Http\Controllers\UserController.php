<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Get all users for admin
     */
    public function index(Request $request)
    {
        $query = User::orderBy('created_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Role filter
        if ($request->has('role') && $request->role !== 'all') {
            $query->role($request->role);
        }

        // Status filter
        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } else {
                $query->where('is_active', false);
            }
        }

        $users = $query->paginate($request->get('per_page', 20));

        // Transform data to include role display name
        $users->getCollection()->transform(function ($user) {
            $user->role_display_name = $user->getRoleDisplayName();
            $user->status = $user->is_active ? 'active' : 'inactive';
            return $user;
        });

        return response()->json([
            'success' => true,
            'data' => $users->items(),
            'pagination' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
            ]
        ]);
    }

    /**
     * Get single user by ID
     */
    public function show($id)
    {
        $user = User::findOrFail($id);
        $user->role_display_name = $user->getRoleDisplayName();
        $user->status = $user->is_active ? 'active' : 'inactive';

        return response()->json([
            'success' => true,
            'data' => $user
        ]);
    }

    /**
     * Store new user
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,kepala_sekolah,guru,staff,user',
            'position' => 'nullable|string|max:100',
            'employee_id' => 'nullable|string|max:50|unique:users,employee_id',
            'bio' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'position' => $request->position,
            'employee_id' => $request->employee_id,
            'bio' => $request->bio,
            'is_active' => $request->get('is_active', true),
        ]);

        $user->role_display_name = $user->getRoleDisplayName();
        $user->status = $user->is_active ? 'active' : 'inactive';

        return response()->json([
            'success' => true,
            'message' => 'User created successfully',
            'data' => $user
        ], 201);
    }

    /**
     * Update user
     */
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,kepala_sekolah,guru,staff,user',
            'position' => 'nullable|string|max:100',
            'employee_id' => ['nullable', 'string', 'max:50', Rule::unique('users')->ignore($user->id)],
            'bio' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'role' => $request->role,
            'position' => $request->position,
            'employee_id' => $request->employee_id,
            'bio' => $request->bio,
            'is_active' => $request->get('is_active', $user->is_active),
        ];

        // Only update password if provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        $user->role_display_name = $user->getRoleDisplayName();
        $user->status = $user->is_active ? 'active' : 'inactive';

        return response()->json([
            'success' => true,
            'message' => 'User updated successfully',
            'data' => $user
        ]);
    }

    /**
     * Delete user
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);
        
        // Prevent deleting current user
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'error' => 'Cannot delete your own account'
            ], 400);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Toggle user status
     */
    public function toggleStatus($id)
    {
        $user = User::findOrFail($id);
        
        // Prevent deactivating current user
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'error' => 'Cannot deactivate your own account'
            ], 400);
        }

        $user->update(['is_active' => !$user->is_active]);

        $user->role_display_name = $user->getRoleDisplayName();
        $user->status = $user->is_active ? 'active' : 'inactive';

        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully',
            'data' => $user
        ]);
    }
}
