import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { motion } from 'framer-motion';
import AdminLayout from '../../components/admin/AdminLayout';
import { Button, Card, Input, Textarea } from '../../components/ui';
import { FadeIn, StaggerContainer, StaggerItem } from '../../components/ui/AnimatedComponents';
import { api } from '../../services/api';

// Validation schema
const settingsSchema = yup.object().shape({
  schoolName: yup
    .string()
    .required('Nama sekolah harus diisi')
    .min(5, 'Nama sekolah minimal 5 karakter')
    .max(100, 'Nama sekolah maksimal 100 karakter'),
  schoolShortName: yup
    .string()
    .required('<PERSON>a singkat sekolah harus diisi')
    .min(3, '<PERSON>a singkat minimal 3 karakter')
    .max(20, 'Nama singkat maksimal 20 karakter'),
  schoolAddress: yup
    .string()
    .required('Alamat sekolah harus diisi')
    .min(10, 'Alamat minimal 10 karakter')
    .max(200, 'Alamat maksimal 200 karakter'),
  schoolPhone: yup
    .string()
    .required('Nomor telepon harus diisi')
    .matches(/^(\+62|62|0)[0-9]{8,13}$/, 'Format nomor telepon tidak valid'),
  schoolEmail: yup
    .string()
    .email('Format email tidak valid')
    .required('Email sekolah harus diisi'),
  schoolWebsite: yup
    .string()
    .url('Format website tidak valid')
    .required('Website sekolah harus diisi'),
  principalName: yup
    .string()
    .required('Nama kepala sekolah harus diisi')
    .min(3, 'Nama kepala sekolah minimal 3 karakter')
    .max(100, 'Nama kepala sekolah maksimal 100 karakter'),
  schoolMotto: yup
    .string()
    .required('Motto sekolah harus diisi')
    .min(5, 'Motto minimal 5 karakter')
    .max(200, 'Motto maksimal 200 karakter'),
  schoolDescription: yup
    .string()
    .required('Deskripsi sekolah harus diisi')
    .min(20, 'Deskripsi minimal 20 karakter')
    .max(1000, 'Deskripsi maksimal 1000 karakter')
});

const SettingsManagement = () => {
  const [settings, setSettings] = useState({
    schoolName: '',
    schoolShortName: '',
    schoolAddress: '',
    schoolPhone: '',
    schoolEmail: '',
    schoolWebsite: '',
    principalName: '',
    schoolMotto: '',
    schoolDescription: '',
    logoUrl: ''
  });

  const [selectedLogo, setSelectedLogo] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // React Hook Form
  const {
    register,
    handleSubmit: onSubmit,
    formState: { errors: formErrors, isSubmitting },
    reset,
    setValue
  } = useForm({
    resolver: yupResolver(settingsSchema),
    defaultValues: settings
  });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.get('/settings');

        if (response.data.success) {
          console.log('Settings data received:', response.data.data); // Debug log
          setSettings(response.data.data);
          reset(response.data.data);
        } else {
          throw new Error('Failed to fetch settings');
        }
      } catch (err) {
        console.error('Error fetching settings:', err);
        setError('Terjadi kesalahan saat memuat pengaturan');

        // Fallback to localStorage
        const savedSettings = localStorage.getItem('schoolSettings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          setSettings(parsedSettings);
          reset(parsedSettings);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [reset]);

  const handleFormSubmit = async (data) => {
    setIsSaving(true);
    try {
      const response = await api.put('/settings', {
        ...data,
        logoUrl: settings.logoUrl
      });

      const result = response.data;

      if (result.success) {
        const updatedSettings = result.data;
        setSettings(updatedSettings);

        // Update localStorage as cache
        localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));

        // Trigger custom event to update other components
        window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
          detail: updatedSettings
        }));

        alert('Pengaturan berhasil disimpan!');
      } else {
        throw new Error(result.error || 'Failed to save settings');
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      alert('Terjadi kesalahan saat menyimpan pengaturan: ' + err.message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        alert('Tipe file tidak didukung. Gunakan JPG, PNG, atau SVG');
        return;
      }

      // Validate file size (max 2MB)
      const maxSize = 2 * 1024 * 1024;
      if (file.size > maxSize) {
        alert('Ukuran file terlalu besar. Maksimal 2MB');
        return;
      }

      setSelectedLogo(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleLogoSave = async () => {
    if (selectedLogo && logoPreview) {
      setIsSaving(true);
      try {
        // Create FormData for file upload
        const formData = new FormData();
        formData.append('logo', selectedLogo);

        // Upload logo to server
        const response = await api.upload('/settings/logo', formData);

        if (response.data.success) {
          const updatedSettings = {
            ...settings,
            logoUrl: response.data.logoUrl // Use the uploaded file URL from server
          };

          setSettings(updatedSettings);
          localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));

          // Trigger custom event
          window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
            detail: updatedSettings
          }));

          setSelectedLogo(null);
          setLogoPreview(null);

          alert('Logo berhasil diperbarui!');
        } else {
          throw new Error('Failed to upload logo');
        }
      } catch (err) {
        console.error('Error uploading logo:', err);
        alert('Terjadi kesalahan saat mengupload logo: ' + (err.message || 'Unknown error'));
      } finally {
        setIsSaving(false);
      }
    }
  };

  const handleLogoReset = () => {
    const defaultLogo = '/images/logo-school.png';
    const updatedSettings = {
      ...settings,
      logoUrl: defaultLogo
    };
    
    setSettings(updatedSettings);
    localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));
    
    window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', { 
      detail: updatedSettings 
    }));
    
    alert('Logo berhasil direset ke default!');
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex items-center justify-center min-h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Memuat pengaturan...</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            <h3 className="font-semibold">Error</h3>
            <p>{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Muat Ulang
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <FadeIn direction="down" className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Pengaturan Sekolah</h1>
          <p className="text-gray-600 mt-2">Kelola informasi dan pengaturan umum sekolah</p>
        </FadeIn>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Logo Settings */}
          <FadeIn delay={0.2} className="lg:col-span-1">
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Logo Sekolah</h2>
              
              <div className="text-center">
                <div className="mb-6">
                  {logoPreview || settings.logoUrl ? (
                    <div className="relative">
                      <img
                        src={logoPreview || settings.logoUrl}
                        alt="Logo Sekolah"
                        className="w-32 h-32 mx-auto object-contain bg-gray-50 rounded-lg border-2 border-gray-200 shadow-sm"
                        onError={(e) => {
                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjgiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K';
                        }}
                      />
                      {logoPreview && (
                        <div className="absolute -top-2 -right-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Preview
                          </span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-32 h-32 mx-auto bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                      <div className="text-center">
                        <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p className="text-xs text-gray-500">Belum ada logo</p>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="space-y-3">
                  <label htmlFor="logo-upload" className="block">
                    <input
                      id="logo-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                      className="hidden"
                    />
                    <Button
                      variant="primary"
                      size="sm"
                      className="cursor-pointer"
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      }
                    >
                      {logoPreview ? 'Ganti Logo' : 'Upload Logo Baru'}
                    </Button>
                  </label>
                  
                  {logoPreview && (
                    <div className="flex space-x-2">
                      <Button
                        variant="success"
                        size="sm"
                        onClick={handleLogoSave}
                        loading={isSaving}
                        disabled={isSaving}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        }
                      >
                        Simpan Logo
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setLogoPreview(null);
                          setSelectedLogo(null);
                        }}
                        disabled={isSaving}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        }
                      >
                        Batal
                      </Button>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleLogoReset}
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    }
                  >
                    Reset ke Default
                  </Button>
                </div>
                
                <p className="text-xs text-gray-500 mt-4">
                  Format: JPG, PNG, SVG<br />
                  Ukuran maksimal: 2MB<br />
                  Rekomendasi: 512x512px
                </p>
              </div>
            </Card>
          </FadeIn>

          {/* School Information Form */}
          <FadeIn delay={0.4} className="lg:col-span-2">
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Informasi Sekolah</h2>
              
              <form onSubmit={onSubmit(handleFormSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nama Sekolah Lengkap"
                    placeholder="SMA Negeri 1 Jakarta"
                    autoComplete="organization"
                    required
                    error={formErrors.schoolName?.message}
                    {...register('schoolName')}
                  />

                  <Input
                    label="Nama Singkat"
                    placeholder="SMAN 1 Jakarta"
                    autoComplete="off"
                    required
                    error={formErrors.schoolShortName?.message}
                    {...register('schoolShortName')}
                  />
                </div>

                <Textarea
                  label="Alamat Sekolah"
                  placeholder="Jl. Pendidikan No. 123, Menteng, Jakarta Pusat"
                  rows={3}
                  autoComplete="street-address"
                  required
                  error={formErrors.schoolAddress?.message}
                  {...register('schoolAddress')}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nomor Telepon"
                    placeholder="021-12345678"
                    type="tel"
                    autoComplete="tel"
                    required
                    error={formErrors.schoolPhone?.message}
                    {...register('schoolPhone')}
                  />

                  <Input
                    label="Email Sekolah"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    required
                    error={formErrors.schoolEmail?.message}
                    {...register('schoolEmail')}
                  />
                </div>

                <Input
                  label="Website Sekolah"
                  type="url"
                  placeholder="https://www.sekolah.sch.id"
                  autoComplete="url"
                  required
                  error={formErrors.schoolWebsite?.message}
                  {...register('schoolWebsite')}
                />

                <Input
                  label="Nama Kepala Sekolah"
                  placeholder="Dr. Ahmad Suryadi, M.Pd"
                  autoComplete="name"
                  required
                  error={formErrors.principalName?.message}
                  {...register('principalName')}
                />

                <Input
                  label="Motto Sekolah"
                  placeholder="Unggul dalam Prestasi, Berkarakter, dan Berwawasan Global"
                  autoComplete="off"
                  required
                  error={formErrors.schoolMotto?.message}
                  {...register('schoolMotto')}
                />

                <Textarea
                  label="Deskripsi Sekolah"
                  placeholder="Deskripsi singkat tentang sekolah..."
                  rows={4}
                  autoComplete="off"
                  required
                  error={formErrors.schoolDescription?.message}
                  {...register('schoolDescription')}
                />

                <div className="flex justify-end pt-4">
                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    loading={isSubmitting || isSaving}
                    disabled={isSubmitting || isSaving}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    }
                  >
                    Simpan Pengaturan
                  </Button>
                </div>
              </form>
            </Card>
          </FadeIn>
        </div>


      </div>
    </AdminLayout>
  );
};

export default SettingsManagement;
