<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\GalleryController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'OK',
        'message' => 'School Management API is running',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
});

// Public routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// News routes (public)
Route::prefix('news')->group(function () {
    Route::get('/', [NewsController::class, 'index']);
    Route::get('/{id}', [NewsController::class, 'show']);
});

// Gallery routes (public)
Route::prefix('gallery')->group(function () {
    Route::get('/', [GalleryController::class, 'index']);
    Route::get('/{id}', [GalleryController::class, 'show']);
});

// Settings routes (public)
Route::get('/settings', [SettingsController::class, 'index']);

// Protected routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    
    // Auth routes
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
    });

    // Dashboard routes
    Route::prefix('dashboard')->group(function () {
        Route::get('/stats', [DashboardController::class, 'getStats']);
        Route::get('/recent-news', [DashboardController::class, 'getRecentNews']);
        Route::get('/recent-activities', [DashboardController::class, 'getRecentActivities']);
    });

    // Admin only routes
    Route::middleware('admin')->group(function () {

        // User management
        Route::post('/auth/register', [AuthController::class, 'register']);
        
        // News management
        Route::prefix('news')->group(function () {
            Route::get('/admin/all', [NewsController::class, 'adminIndex']);
            Route::post('/', [NewsController::class, 'store']);
            Route::put('/{id}', [NewsController::class, 'update']);
            Route::delete('/{id}', [NewsController::class, 'destroy']);
        });

        // Gallery management
        Route::prefix('gallery')->group(function () {
            Route::get('/admin/all', [GalleryController::class, 'adminIndex']);
            Route::post('/', [GalleryController::class, 'store']);
            Route::put('/{id}', [GalleryController::class, 'update']);
            Route::delete('/{id}', [GalleryController::class, 'destroy']);
        });

        // Settings management
        Route::prefix('settings')->group(function () {
            Route::get('/admin/all', [SettingsController::class, 'adminIndex']);
            Route::put('/', [SettingsController::class, 'update']);
            Route::post('/', [SettingsController::class, 'store']);
            Route::get('/{key}', [SettingsController::class, 'show']);
            Route::delete('/{key}', [SettingsController::class, 'destroy']);
        });
    });
});

// Fallback route
Route::fallback(function () {
    return response()->json([
        'error' => 'Endpoint not found',
        'message' => 'The requested API endpoint does not exist'
    ], 404);
});
